/**
 * Webhook Queue Routes
 *
 * HTTP route definitions for the webhook queue system. Provides endpoints
 * for webhook ingestion, queue processing, and monitoring.
 *
 * @fileoverview Queue system HTTP routes
 * @version 1.0.0
 * @since 2025-08-06
 */

import { Hono } from "hono";
import {
	addWebhookToQueue,
	processQueue,
	getQueueStatistics,
} from "@/handlers/queueHandler";

/**
 * Create queue routes
 *
 * Sets up all HTTP routes for the webhook queue system including
 * webhook ingestion, processing triggers, and monitoring endpoints.
 *
 * **Available Routes:**
 * - POST /webhook - Add webhook to queue
 * - POST /process - Trigger queue processing
 * - GET /stats - Get queue statistics
 *
 * @returns Hono app with queue routes configured
 */
export function createQueueRoutes(): Hono {
	const app = new Hono();

	/**
	 * Add webhook to queue
	 * POST /api/queue/webhook
	 */
	app.post("/webhook", addWebhookToQueue);

	/**
	 * Trigger queue processing
	 * POST /api/queue/process
	 */
	app.post("/process", processQueue);

	/**
	 * Get queue statistics
	 * GET /api/queue/stats
	 */
	app.get("/stats", getQueueStatistics);

	/**
	 * Health check endpoint
	 * GET /api/queue/health
	 */
	app.get("/health", async (c) => {
		return c.json({
			status: "healthy",
			service: "webhook-queue",
			timestamp: new Date().toISOString(),
		});
	});

	return app;
}

/**
 * Example integration with existing webhook handlers
 *
 * Shows how to integrate the queue system with existing webhook handlers
 * for seamless processing without breaking existing functionality.
 */
export const queueIntegrationExample = {
	/**
	 * Example: Integrate with CC webhook handler
	 */
	async integrateWithCCWebhook(payload: any, context: any) {
		// This would be added to the existing CC webhook handler
		const { fireAndForgetWebhookIngestionFromContext } = await import("@/queue/fireAndForgetIntegration");
		
		// Add to queue for processing
		fireAndForgetWebhookIngestionFromContext(context, {
			source: "cc",
			entityType: "patient",
			entityId: payload.id.toString(),
			patientId: payload.patientId, // if available
			payload: payload,
		});
	},

	/**
	 * Example: Integrate with AP webhook handler
	 */
	async integrateWithAPWebhook(payload: any, context: any) {
		// This would be added to the existing AP webhook handler
		const { fireAndForgetWebhookIngestionFromContext } = await import("@/queue/fireAndForgetIntegration");
		
		// Add to queue for processing
		fireAndForgetWebhookIngestionFromContext(context, {
			source: "ap",
			entityType: "contact",
			entityId: payload.contact_id,
			patientId: payload.patientId, // if available from lookup
			payload: payload,
		});
	},
};
