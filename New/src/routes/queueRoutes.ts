/**
 * Webhook Queue Routes
 *
 * HTTP route definitions for the webhook queue system. Provides endpoints
 * for webhook ingestion, queue processing, and monitoring.
 *
 * @fileoverview Queue system HTTP routes
 * @version 1.0.0
 * @since 2025-08-06
 */

import type { APContactWebhookPayload } from "@type";
import type { Context } from "hono";
import { Hono } from "hono";
import {
	addWebhookToQueue,
	getQueueStatistics,
	processQueue,
} from "@/handlers/queueHandler";
import type { CCWebhookPayload } from "@/processors/ccWebhook";

/**
 * Create queue routes
 *
 * Sets up all HTTP routes for the webhook queue system including
 * webhook ingestion, processing triggers, and monitoring endpoints.
 *
 * **Available Routes:**
 * - POST /webhook - Add webhook to queue
 * - POST /process - Trigger queue processing
 * - GET /stats - Get queue statistics
 *
 * @returns Hono app with queue routes configured
 */
export function createQueueRoutes(): Hono {
	const app = new Hono();

	/**
	 * Add webhook to queue
	 * POST /api/queue/webhook
	 */
	app.post("/webhook", addWebhookToQueue);

	/**
	 * Trigger queue processing
	 * POST /api/queue/process
	 */
	app.post("/process", processQueue);

	/**
	 * Get queue statistics
	 * GET /api/queue/stats
	 */
	app.get("/stats", getQueueStatistics);

	/**
	 * Health check endpoint
	 * GET /api/queue/health
	 */
	app.get("/health", async (c) => {
		return c.json({
			status: "healthy",
			service: "webhook-queue",
			timestamp: new Date().toISOString(),
		});
	});

	return app;
}

/**
 * Example integration with existing webhook handlers
 *
 * Shows how to integrate the queue system with existing webhook handlers
 * for seamless processing without breaking existing functionality.
 */
export const queueIntegrationExample = {
	/**
	 * Example: Integrate with CC webhook handler
	 */
	async integrateWithCCWebhook(payload: CCWebhookPayload) {
		// This would be added to the existing CC webhook handler
		const { fireAndForgetWebhookIngestionFromContext } = await import(
			"@/queue/fireAndForgetIntegration"
		);

		// Add to queue for processing
		fireAndForgetWebhookIngestionFromContext({
			source: "cc",
			entityType: "patient",
			entityId: payload.id.toString(),
			patientId: undefined, // Would need to be looked up from patient table
			payload: payload,
		});
	},

	/**
	 * Example: Integrate with AP webhook handler
	 */
	async integrateWithAPWebhook(
		payload: APContactWebhookPayload,
	) {
		// This would be added to the existing AP webhook handler
		const { fireAndForgetWebhookIngestionFromContext } = await import(
			"@/queue/fireAndForgetIntegration"
		);

		// Add to queue for processing
		fireAndForgetWebhookIngestionFromContext({
			source: "ap",
			entityType: "contact",
			entityId: String(payload.contact_id),
			patientId: undefined, // Would need to be looked up from patient table
			payload: payload,
		});
	},
};
