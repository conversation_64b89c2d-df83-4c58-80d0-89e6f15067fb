/**
 * Webhook Queue System
 *
 * Main entry point for the webhook queue system providing a unified interface
 * for queue management, processing, and monitoring. Integrates with the existing
 * fire-and-forget utilities for immediate processing triggers.
 *
 * @fileoverview Webhook queue system main module
 * @version 1.0.0
 * @since 2025-08-06
 */

// Core queue management
export { WebhookQueueManager } from "./WebhookQueueManager";
export { WebhookTimeoutDetector } from "./timeoutDetector";

// Type definitions
export type {
	WebhookQueueStatus,
	WebhookQueueLogStatus,
	AlertType,
	AlertSeverity,
	LockType,
	PlatformSource,
	WebhookPayload,
	WebhookQueueInsert,
	WebhookQueueItem,
	WebhookQueueLogItem,
	DuplicatePreventionLock,
	Alert,
	DuplicateDetectionResult,
	WebhookProcessingResult,
	BatchProcessingResult,
	QueueProcessingOptions,
	TimeoutDetectionResult,
	QueueStatistics,
} from "./types";

// API handlers
export {
	addWebhookToQueue,
	processQueue,
	getQueueStatistics,
} from "@/handlers/queueHandler";

// Utility functions
export {
	fireAndForgetQueueProcessing,
	fireAndForgetWebhookIngestion,
} from "./fireAndForgetIntegration";
