/**
 * Webhook Timeout Detection System
 *
 * Specialized module for detecting and handling webhook processing timeouts.
 * Implements the critical requirement that timeout detection is based on
 * processing duration (from started_at timestamp) rather than absolute time.
 *
 * **Key Features:**
 * - 29-second timeout detection based on processing start time
 * - Automatic alert creation for timed-out webhooks
 * - Log movement for audit trail preservation
 * - Batch processing for efficiency
 * - Comprehensive error handling and logging
 *
 * @fileoverview Webhook timeout detection and handling
 * @version 1.0.0
 * @since 2025-08-06
 */

import { and, eq, lt, isNotNull } from "drizzle-orm";
import { getDb, dbSchema } from "@database";
import { logError, logInfo, logDebug, logWarn } from "@/utils/logger";
import type { 
	WebhookQueueItem, 
	TimeoutDetectionResult,
	Alert 
} from "./types";

/**
 * Timeout detection configuration
 */
interface TimeoutConfig {
	/** Timeout threshold in milliseconds (default: 29000) */
	timeoutMs: number;
	/** Maximum number of webhooks to process in one batch */
	batchSize: number;
	/** Whether to create alerts for timeouts */
	createAlerts: boolean;
	/** Whether to move timed-out webhooks to logs */
	moveToLogs: boolean;
}

/**
 * Default timeout configuration
 */
const DEFAULT_TIMEOUT_CONFIG: TimeoutConfig = {
	timeoutMs: 29000, // 29 seconds
	batchSize: 50,
	createAlerts: true,
	moveToLogs: true,
};

/**
 * Webhook Timeout Detector
 *
 * Handles detection and processing of webhook timeouts based on the critical
 * requirement that timeouts are measured from processing start time, not
 * request arrival time.
 *
 * **Timeout Logic:**
 * 1. Find webhooks with status = 'processing'
 * 2. Check if started_at timestamp is older than 29 seconds
 * 3. Calculate actual processing duration from started_at
 * 4. Create alerts with processing duration context
 * 5. Move to logs with timeout status
 * 6. Remove from active queue
 *
 * **Critical Implementation Details:**
 * - Uses started_at timestamp (set when status changes to 'processing')
 * - Measures duration from processing start, not webhook creation
 * - Preserves exact processing duration in alerts and logs
 * - Handles edge cases (null started_at, clock skew, etc.)
 */
export class WebhookTimeoutDetector {
	private db = getDb();
	private config: TimeoutConfig;

	constructor(config: Partial<TimeoutConfig> = {}) {
		this.config = { ...DEFAULT_TIMEOUT_CONFIG, ...config };
	}

	/**
	 * Detect and handle all timed-out webhooks
	 *
	 * Main entry point for timeout detection. Finds all webhooks that have
	 * exceeded the processing timeout based on their started_at timestamp
	 * and handles them according to configuration.
	 *
	 * **Processing Flow:**
	 * 1. Query for processing webhooks with started_at older than threshold
	 * 2. Calculate exact processing duration for each webhook
	 * 3. Create timeout alerts with processing context
	 * 4. Move webhooks to logs with timeout status
	 * 5. Remove from active queue
	 * 6. Return comprehensive results
	 *
	 * @returns Timeout detection results including processed webhooks and alerts
	 */
	async detectAndHandleTimeouts(): Promise<TimeoutDetectionResult> {
		const startTime = Date.now();
		
		try {
			logDebug("Starting timeout detection", {
				timeoutMs: this.config.timeoutMs,
				batchSize: this.config.batchSize,
			});

			// Calculate timeout threshold based on processing start time
			const timeoutThreshold = new Date(Date.now() - this.config.timeoutMs);

			// Find webhooks that are processing and started before threshold
			const timedOutWebhooks = await this.findTimedOutWebhooks(timeoutThreshold);

			if (timedOutWebhooks.length === 0) {
				logDebug("No timed-out webhooks found");
				return { timedOutWebhooks: [], alertsCreated: 0 };
			}

			logWarn("Detected timed-out webhooks", {
				count: timedOutWebhooks.length,
				timeoutThreshold: timeoutThreshold.toISOString(),
			});

			// Process each timed-out webhook
			const results = await this.processTimedOutWebhooks(timedOutWebhooks);

			const totalTime = Date.now() - startTime;
			logInfo("Timeout detection completed", {
				timedOutCount: timedOutWebhooks.length,
				alertsCreated: results.alertsCreated,
				processingTime: totalTime,
			});

			return results;
		} catch (error) {
			logError("Failed to detect timeouts", error);
			return { timedOutWebhooks: [], alertsCreated: 0 };
		}
	}

	/**
	 * Find webhooks that have exceeded processing timeout
	 *
	 * Queries the database for webhooks that are currently processing
	 * and have a started_at timestamp older than the timeout threshold.
	 *
	 * **Query Logic:**
	 * - status = 'processing' (actively being processed)
	 * - started_at IS NOT NULL (has actually started processing)
	 * - started_at < timeout_threshold (exceeded timeout duration)
	 *
	 * @param timeoutThreshold - Timestamp threshold for timeout detection
	 * @returns Array of timed-out webhook items
	 */
	private async findTimedOutWebhooks(timeoutThreshold: Date): Promise<WebhookQueueItem[]> {
		try {
			const webhooks = await this.db
				.select()
				.from(dbSchema.webhookQueue)
				.where(
					and(
						eq(dbSchema.webhookQueue.status, "processing"),
						isNotNull(dbSchema.webhookQueue.startedAt),
						lt(dbSchema.webhookQueue.startedAt, timeoutThreshold)
					)
				)
				.limit(this.config.batchSize);

			logDebug("Found timed-out webhooks", {
				count: webhooks.length,
				timeoutThreshold: timeoutThreshold.toISOString(),
			});

			return webhooks;
		} catch (error) {
			logError("Failed to query timed-out webhooks", error);
			return [];
		}
	}

	/**
	 * Process each timed-out webhook
	 *
	 * Handles the complete processing of timed-out webhooks including
	 * alert creation, log movement, and queue cleanup.
	 *
	 * @param timedOutWebhooks - Array of timed-out webhooks to process
	 * @returns Processing results with alert count
	 */
	private async processTimedOutWebhooks(
		timedOutWebhooks: WebhookQueueItem[]
	): Promise<TimeoutDetectionResult> {
		let alertsCreated = 0;
		const processedWebhooks: WebhookQueueItem[] = [];

		for (const webhook of timedOutWebhooks) {
			try {
				// Calculate exact processing duration from started_at
				const processingDuration = this.calculateProcessingDuration(webhook);

				logWarn("Processing timed-out webhook", {
					webhookId: webhook.id,
					source: webhook.source,
					entityType: webhook.entityType,
					entityId: webhook.entityId,
					startedAt: webhook.startedAt?.toISOString(),
					processingDuration,
				});

				// Create timeout alert if configured
				if (this.config.createAlerts) {
					await this.createTimeoutAlert(webhook, processingDuration);
					alertsCreated++;
				}

				// Move to logs if configured
				if (this.config.moveToLogs) {
					await this.moveWebhookToLogs(webhook, processingDuration);
				}

				// Remove from active queue
				await this.removeFromQueue(webhook.id);

				processedWebhooks.push(webhook);

				logInfo("Handled timed-out webhook", {
					webhookId: webhook.id,
					processingDuration,
					alertCreated: this.config.createAlerts,
					movedToLogs: this.config.moveToLogs,
				});
			} catch (error) {
				logError("Failed to process timed-out webhook", {
					webhookId: webhook.id,
					error: error instanceof Error ? error.message : String(error),
				});
				// Continue processing other webhooks even if one fails
			}
		}

		return {
			timedOutWebhooks: processedWebhooks,
			alertsCreated,
		};
	}

	/**
	 * Calculate processing duration from started_at timestamp
	 *
	 * Calculates the exact processing duration based on the webhook's
	 * started_at timestamp. Handles edge cases like null timestamps.
	 *
	 * @param webhook - Webhook to calculate duration for
	 * @returns Processing duration in milliseconds
	 */
	private calculateProcessingDuration(webhook: WebhookQueueItem): number {
		if (!webhook.startedAt) {
			logWarn("Webhook has no started_at timestamp", {
				webhookId: webhook.id,
			});
			// Fallback to creation time if started_at is missing
			return Date.now() - webhook.createdAt.getTime();
		}

		const duration = Date.now() - webhook.startedAt.getTime();
		
		// Sanity check for negative durations (clock skew)
		if (duration < 0) {
			logWarn("Negative processing duration detected", {
				webhookId: webhook.id,
				startedAt: webhook.startedAt.toISOString(),
				duration,
			});
			return 0;
		}

		return duration;
	}

	/**
	 * Create timeout alert
	 *
	 * Creates a comprehensive alert for the timed-out webhook with
	 * processing context and debugging information.
	 *
	 * @param webhook - Timed-out webhook
	 * @param processingDuration - Actual processing duration in milliseconds
	 */
	private async createTimeoutAlert(
		webhook: WebhookQueueItem,
		processingDuration: number
	): Promise<void> {
		try {
			await this.db.insert(dbSchema.alerts).values({
				id: crypto.randomUUID(),
				alertType: "webhook_timeout",
				severity: "high",
				title: "Webhook Processing Timeout",
				description: `Webhook ${webhook.id} exceeded ${this.config.timeoutMs}ms processing limit (actual: ${processingDuration}ms)`,
				webhookId: webhook.id,
				patientId: webhook.patientId,
				appointmentId: webhook.appointmentId,
				context: {
					source: webhook.source,
					entityType: webhook.entityType,
					entityId: webhook.entityId,
					processingDuration,
					timeoutThreshold: this.config.timeoutMs,
					startedAt: webhook.startedAt?.toISOString(),
					retryCount: webhook.retryCount,
					priority: webhook.priority,
				},
				resolved: 0,
			});

			logDebug("Created timeout alert", {
				webhookId: webhook.id,
				processingDuration,
			});
		} catch (error) {
			logError("Failed to create timeout alert", {
				webhookId: webhook.id,
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}

	/**
	 * Move webhook to logs table
	 *
	 * Moves the timed-out webhook to the logs table with timeout status
	 * and processing duration information.
	 *
	 * @param webhook - Webhook to move to logs
	 * @param processingDuration - Processing duration in milliseconds
	 */
	private async moveWebhookToLogs(
		webhook: WebhookQueueItem,
		processingDuration: number
	): Promise<void> {
		try {
			await this.db.insert(dbSchema.webhookQueueLogs).values({
				id: webhook.id, // Keep same ID for traceability
				createdAt: webhook.createdAt,
				updatedAt: new Date(),
				status: "timeout",
				priority: webhook.priority,
				source: webhook.source,
				entityType: webhook.entityType,
				entityId: webhook.entityId,
				patientId: webhook.patientId,
				appointmentId: webhook.appointmentId,
				payload: webhook.payload,
				startedAt: webhook.startedAt,
				completedAt: new Date(),
				retryCount: webhook.retryCount,
				errorMessage: `Processing timeout after ${processingDuration}ms (limit: ${this.config.timeoutMs}ms)`,
				processingTimeMs: processingDuration,
			});

			logDebug("Moved webhook to logs", {
				webhookId: webhook.id,
				processingDuration,
			});
		} catch (error) {
			logError("Failed to move webhook to logs", {
				webhookId: webhook.id,
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}

	/**
	 * Remove webhook from active queue
	 *
	 * @param webhookId - ID of webhook to remove
	 */
	private async removeFromQueue(webhookId: string): Promise<void> {
		try {
			await this.db
				.delete(dbSchema.webhookQueue)
				.where(eq(dbSchema.webhookQueue.id, webhookId));

			logDebug("Removed webhook from queue", { webhookId });
		} catch (error) {
			logError("Failed to remove webhook from queue", {
				webhookId,
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}
}
