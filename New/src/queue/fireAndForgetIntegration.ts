/**
 * Fire-and-Forget Queue Integration
 *
 * Integration layer between the webhook queue system and the existing
 * fire-and-forget utilities. Provides convenient functions for triggering
 * queue operations without blocking the main request flow.
 *
 * **Key Features:**
 * - Immediate queue processing triggers
 * - Webhook ingestion with automatic processing
 * - Integration with existing fireAndForgetRequest utility
 * - Proper error handling and logging
 * - Context extraction from Hono requests
 *
 * @fileoverview Fire-and-forget integration for webhook queue
 * @version 1.0.0
 * @since 2025-08-06
 */

import type { Context } from "hono";
import { fireAndForgetRequest } from "@/utils/fireAndForget";
import { logError, logInfo, logDebug } from "@/utils/logger";
import type { WebhookQueueInsert, QueueProcessingOptions } from "./types";

/**
 * Fire-and-forget queue processing trigger
 *
 * Triggers queue processing via HTTP request without waiting for completion.
 * Designed to be called immediately after adding webhooks to the queue for
 * real-time processing.
 *
 * **Use Cases:**
 * - Immediate processing after webhook ingestion
 * - Periodic queue processing triggers
 * - Manual queue processing from admin interfaces
 * - Recovery processing after system restarts
 *
 * @param baseUrl - Base URL for the application
 * @param options - Optional processing options
 * @param context - Additional context for logging
 */
export function fireAndForgetQueueProcessing(
	baseUrl: string,
	options: QueueProcessingOptions = {},
	context: Record<string, unknown> = {}
): void {
	try {
		const endpoint = "/api/queue/process";
		const fullUrl = `${baseUrl}${endpoint}`;

		logDebug("Triggering fire-and-forget queue processing", {
			url: fullUrl,
			options,
			...context,
		});

		fireAndForgetRequest(fullUrl, {
			method: "POST",
			headers: { "Content-Type": "application/json" },
			body: {
				triggerId: crypto.randomUUID(),
				batchSize: options.batchSize || 3,
				maxProcessingTimeMs: options.maxProcessingTimeMs || 29000,
			},
			logContext: {
				trigger: "fire_and_forget_processing",
				timestamp: new Date().toISOString(),
				...context,
			},
		});

		logInfo("Queue processing trigger sent", {
			url: fullUrl,
			...context,
		});
	} catch (error) {
		logError("Failed to trigger fire-and-forget queue processing", {
			error: error instanceof Error ? error.message : String(error),
			baseUrl,
			options,
			...context,
		});
	}
}

/**
 * Fire-and-forget webhook ingestion
 *
 * Adds webhook to queue and immediately triggers processing via fire-and-forget
 * pattern. Provides a complete webhook handling solution in a single function.
 *
 * **Processing Flow:**
 * 1. Add webhook to queue with duplicate detection
 * 2. If webhook is pending (not duplicate), trigger processing
 * 3. Return immediately without waiting for processing completion
 * 4. Processing happens asynchronously in background
 *
 * @param baseUrl - Base URL for the application
 * @param webhook - Webhook data to ingest
 * @param context - Additional context for logging
 */
export function fireAndForgetWebhookIngestion(
	baseUrl: string,
	webhook: WebhookQueueInsert,
	context: Record<string, unknown> = {}
): void {
	try {
		const endpoint = "/api/queue/webhook";
		const fullUrl = `${baseUrl}${endpoint}`;

		logDebug("Triggering fire-and-forget webhook ingestion", {
			url: fullUrl,
			source: webhook.source,
			entityType: webhook.entityType,
			entityId: webhook.entityId,
			...context,
		});

		fireAndForgetRequest(fullUrl, {
			method: "POST",
			headers: { "Content-Type": "application/json" },
			body: webhook,
			logContext: {
				trigger: "fire_and_forget_ingestion",
				source: webhook.source,
				entityType: webhook.entityType,
				entityId: webhook.entityId,
				timestamp: new Date().toISOString(),
				...context,
			},
		});

		logInfo("Webhook ingestion trigger sent", {
			url: fullUrl,
			source: webhook.source,
			entityType: webhook.entityType,
			...context,
		});
	} catch (error) {
		logError("Failed to trigger fire-and-forget webhook ingestion", {
			error: error instanceof Error ? error.message : String(error),
			baseUrl,
			webhook: {
				source: webhook.source,
				entityType: webhook.entityType,
				entityId: webhook.entityId,
			},
			...context,
		});
	}
}

/**
 * Extract base URL from Hono context
 *
 * Utility function to extract the base URL from a Hono request context
 * for use with fire-and-forget triggers.
 *
 * @param c - Hono context object
 * @returns Base URL string
 */
export function extractBaseUrl(c: Context): string {
	try {
		const url = new URL(c.req.url);
		return `${url.protocol}//${url.host}`;
	} catch (error) {
		logError("Failed to extract base URL from context", {
			error: error instanceof Error ? error.message : String(error),
			url: c.req.url,
		});
		// Fallback to a default or throw
		throw new Error("Unable to extract base URL from request context");
	}
}

/**
 * Fire-and-forget queue processing with context extraction
 *
 * Convenience function that extracts base URL from Hono context and
 * triggers queue processing. Ideal for use in webhook handlers.
 *
 * @param c - Hono context object
 * @param options - Optional processing options
 * @param context - Additional context for logging
 */
export function fireAndForgetQueueProcessingFromContext(
	c: Context,
	options: QueueProcessingOptions = {},
	context: Record<string, unknown> = {}
): void {
	try {
		const baseUrl = extractBaseUrl(c);
		fireAndForgetQueueProcessing(baseUrl, options, {
			requestUrl: c.req.url,
			method: c.req.method,
			...context,
		});
	} catch (error) {
		logError("Failed to trigger queue processing from context", {
			error: error instanceof Error ? error.message : String(error),
			url: c.req.url,
			...context,
		});
	}
}

/**
 * Fire-and-forget webhook ingestion with context extraction
 *
 * Convenience function that extracts base URL from Hono context and
 * triggers webhook ingestion. Ideal for use in webhook handlers.
 *
 * @param c - Hono context object
 * @param webhook - Webhook data to ingest
 * @param context - Additional context for logging
 */
export function fireAndForgetWebhookIngestionFromContext(
	c: Context,
	webhook: WebhookQueueInsert,
	context: Record<string, unknown> = {}
): void {
	try {
		const baseUrl = extractBaseUrl(c);
		fireAndForgetWebhookIngestion(baseUrl, webhook, {
			requestUrl: c.req.url,
			method: c.req.method,
			...context,
		});
	} catch (error) {
		logError("Failed to trigger webhook ingestion from context", {
			error: error instanceof Error ? error.message : String(error),
			url: c.req.url,
			webhook: {
				source: webhook.source,
				entityType: webhook.entityType,
				entityId: webhook.entityId,
			},
			...context,
		});
	}
}

/**
 * Batch fire-and-forget processing trigger
 *
 * Triggers multiple queue processing requests with different configurations.
 * Useful for comprehensive queue processing that handles different priorities
 * or processing requirements.
 *
 * @param baseUrl - Base URL for the application
 * @param batchConfigs - Array of processing configurations
 * @param context - Additional context for logging
 */
export function fireAndForgetBatchProcessing(
	baseUrl: string,
	batchConfigs: QueueProcessingOptions[],
	context: Record<string, unknown> = {}
): void {
	try {
		logDebug("Triggering batch fire-and-forget processing", {
			batchCount: batchConfigs.length,
			...context,
		});

		batchConfigs.forEach((config, index) => {
			// Add small delay between requests to avoid overwhelming the system
			setTimeout(() => {
				fireAndForgetQueueProcessing(baseUrl, config, {
					batchIndex: index,
					batchTotal: batchConfigs.length,
					...context,
				});
			}, index * 100); // 100ms delay between requests
		});

		logInfo("Batch processing triggers sent", {
			batchCount: batchConfigs.length,
			...context,
		});
	} catch (error) {
		logError("Failed to trigger batch fire-and-forget processing", {
			error: error instanceof Error ? error.message : String(error),
			batchCount: batchConfigs.length,
			...context,
		});
	}
}

/**
 * Periodic queue processing trigger
 *
 * Sets up periodic queue processing triggers for background maintenance.
 * Useful for ensuring queue processing continues even without new webhooks.
 *
 * **Note:** This should be used carefully in Cloudflare Workers environment
 * as it creates timers that may not persist across requests.
 *
 * @param baseUrl - Base URL for the application
 * @param intervalMs - Interval between processing triggers in milliseconds
 * @param options - Processing options
 * @param context - Additional context for logging
 * @returns Timer ID for cancellation
 */
export function setupPeriodicQueueProcessing(
	baseUrl: string,
	intervalMs: number = 30000, // 30 seconds default
	options: QueueProcessingOptions = {},
	context: Record<string, unknown> = {}
): number {
	logInfo("Setting up periodic queue processing", {
		intervalMs,
		...context,
	});

	return setInterval(() => {
		fireAndForgetQueueProcessing(baseUrl, options, {
			trigger: "periodic",
			intervalMs,
			...context,
		});
	}, intervalMs) as unknown as number;
}
