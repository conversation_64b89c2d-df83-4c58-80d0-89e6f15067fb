{"id": "d574b58f-f90c-4da5-aa6a-8f24a5c1ebc5", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.alerts": {"name": "alerts", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "alert_type": {"name": "alert_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "severity": {"name": "severity", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "webhook_id": {"name": "webhook_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "patient_id": {"name": "patient_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "appointment_id": {"name": "appointment_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "context": {"name": "context", "type": "jsonb", "primaryKey": false, "notNull": false}, "resolved": {"name": "resolved", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "resolved_at": {"name": "resolved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "resolved_by": {"name": "resolved_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.appointments": {"name": "appointments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ap_id": {"name": "ap_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "cc_id": {"name": "cc_id", "type": "integer", "primaryKey": false, "notNull": false}, "patient_id": {"name": "patient_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ap_updated_at": {"name": "ap_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "cc_updated_at": {"name": "cc_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "ap_data": {"name": "ap_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "cc_data": {"name": "cc_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "ap_note_id": {"name": "ap_note_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"appointments_patient_id_patients_id_fk": {"name": "appointments_patient_id_patients_id_fk", "tableFrom": "appointments", "tableTo": "patients", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"appointments_ap_id_unique": {"name": "appointments_ap_id_unique", "nullsNotDistinct": false, "columns": ["ap_id"]}, "appointments_cc_id_unique": {"name": "appointments_cc_id_unique", "nullsNotDistinct": false, "columns": ["cc_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.custom_fields": {"name": "custom_fields", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ap_id": {"name": "ap_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "cc_id": {"name": "cc_id", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ap_config": {"name": "ap_config", "type": "jsonb", "primaryKey": false, "notNull": false}, "cc_config": {"name": "cc_config", "type": "jsonb", "primaryKey": false, "notNull": false}, "mapping_type": {"name": "mapping_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'custom_to_custom'"}, "ap_standard_field": {"name": "ap_standard_field", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "cc_standard_field": {"name": "cc_standard_field", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"custom_fields_ap_id_unique": {"name": "custom_fields_ap_id_unique", "nullsNotDistinct": false, "columns": ["ap_id"]}, "custom_fields_cc_id_unique": {"name": "custom_fields_cc_id_unique", "nullsNotDistinct": false, "columns": ["cc_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.duplicate_prevention_locks": {"name": "duplicate_prevention_locks", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "lock_type": {"name": "lock_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "patient_id": {"name": "patient_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "appointment_id": {"name": "appointment_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "webhook_id": {"name": "webhook_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "context": {"name": "context", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"duplicate_prevention_locks_patient_id_patients_id_fk": {"name": "duplicate_prevention_locks_patient_id_patients_id_fk", "tableFrom": "duplicate_prevention_locks", "tableTo": "patients", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "duplicate_prevention_locks_appointment_id_appointments_id_fk": {"name": "duplicate_prevention_locks_appointment_id_appointments_id_fk", "tableFrom": "duplicate_prevention_locks", "tableTo": "appointments", "columnsFrom": ["appointment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.error_logs": {"name": "error_logs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "request_id": {"name": "request_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "stack": {"name": "stack", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.patients": {"name": "patients", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ap_id": {"name": "ap_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "cc_id": {"name": "cc_id", "type": "integer", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ap_updated_at": {"name": "ap_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "cc_updated_at": {"name": "cc_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "ap_data": {"name": "ap_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "cc_data": {"name": "cc_data", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhook_queue": {"name": "webhook_queue", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": true, "default": 100}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "patient_id": {"name": "patient_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "appointment_id": {"name": "appointment_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": true}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "retry_count": {"name": "retry_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "max_retries": {"name": "max_retries", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "next_retry_at": {"name": "next_retry_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "processing_time_ms": {"name": "processing_time_ms", "type": "integer", "primaryKey": false, "notNull": false}, "duplicate_of": {"name": "duplicate_of", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "duplicate_reason": {"name": "duplicate_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"webhook_queue_patient_id_patients_id_fk": {"name": "webhook_queue_patient_id_patients_id_fk", "tableFrom": "webhook_queue", "tableTo": "patients", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "webhook_queue_appointment_id_appointments_id_fk": {"name": "webhook_queue_appointment_id_appointments_id_fk", "tableFrom": "webhook_queue", "tableTo": "appointments", "columnsFrom": ["appointment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhook_queue_logs": {"name": "webhook_queue_logs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": true}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "patient_id": {"name": "patient_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "appointment_id": {"name": "appointment_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": true}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "retry_count": {"name": "retry_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "processing_time_ms": {"name": "processing_time_ms", "type": "integer", "primaryKey": false, "notNull": false}, "duplicate_of": {"name": "duplicate_of", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "duplicate_reason": {"name": "duplicate_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhooks": {"name": "webhooks", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "request_id": {"name": "request_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": true}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"webhooks_request_id_unique": {"name": "webhooks_request_id_unique", "nullsNotDistinct": false, "columns": ["request_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}